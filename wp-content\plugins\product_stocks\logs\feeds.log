2025-06-13 07:35:25: Започва генериране на XML фийд за търговец: ozone
2025-06-13 07:35:25: Намерени 5 категории за търговеца: ozone
2025-06-13 07:35:25: Кешът за атрибути и категории е изчистен
2025-06-13 07:35:25: Започва извличане на вариации от категории: 19, 47, 158, 202, 215
2025-06-13 07:35:25: Извличане на вариации от категории с директна SQL заявка: Array
(
    [0] => SELECT
                pv.ID as variation_id,
                pv.post_title as variation_name,
                pv.post_parent as parent_id,
                p.post_title as product_name,
                p.post_content as product_description,
                pm_sku.meta_value as sku,
                pm_stock.meta_value as stock,
                pm_stock_status.meta_value as stock_status,
                pm_price.meta_value as price,
                pm_regular_price.meta_value as regular_price,
                pm_sale_price.meta_value as sale_price,
                pm_barcode.meta_value as barcode,
                pm_weight.meta_value as weight,
                pm_length.meta_value as length,
                pm_width.meta_value as width,
                pm_height.meta_value as height,
                pm_thumbnail.meta_value as thumbnail_id,
                pm_gallery.meta_value as gallery_images,
                pm_size.meta_value as size_slug
            FROM {{prefix}}posts pv
            INNER JOIN {{prefix}}posts p ON pv.post_parent = p.ID
            INNER JOIN (
                SELECT DISTINCT p_cat.ID
                FROM {{prefix}}posts p_cat
                INNER JOIN {{prefix}}term_relationships tr_cat ON p_cat.ID = tr_cat.object_id
                INNER JOIN {{prefix}}term_taxonomy tt_cat ON tr_cat.term_taxonomy_id = tt_cat.term_taxonomy_id
                WHERE p_cat.post_type = 'product'
                    AND p_cat.post_status = 'publish'
                    AND tt_cat.taxonomy = 'product_cat'
                    AND tt_cat.term_id IN (%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d)
            ) parent_products ON p.ID = parent_products.ID
            LEFT JOIN {{prefix}}postmeta pm_sku ON pv.ID = pm_sku.post_id AND pm_sku.meta_key = '_sku'
            LEFT JOIN {{prefix}}postmeta pm_stock ON pv.ID = pm_stock.post_id AND pm_stock.meta_key = '_stock'
            LEFT JOIN {{prefix}}postmeta pm_stock_status ON pv.ID = pm_stock_status.post_id AND pm_stock_status.meta_key = '_stock_status'
            LEFT JOIN {{prefix}}postmeta pm_price ON pv.ID = pm_price.post_id AND pm_price.meta_key = '_price'
            LEFT JOIN {{prefix}}postmeta pm_regular_price ON pv.ID = pm_regular_price.post_id AND pm_regular_price.meta_key = '_regular_price'
            LEFT JOIN {{prefix}}postmeta pm_sale_price ON pv.ID = pm_sale_price.post_id AND pm_sale_price.meta_key = '_sale_price'
            LEFT JOIN {{prefix}}postmeta pm_barcode ON pv.ID = pm_barcode.post_id AND pm_barcode.meta_key = '_alg_ean'
            LEFT JOIN {{prefix}}postmeta pm_weight ON pv.ID = pm_weight.post_id AND pm_weight.meta_key = '_weight'
            LEFT JOIN {{prefix}}postmeta pm_length ON pv.ID = pm_length.post_id AND pm_length.meta_key = '_length'
            LEFT JOIN {{prefix}}postmeta pm_width ON pv.ID = pm_width.post_id AND pm_width.meta_key = '_width'
            LEFT JOIN {{prefix}}postmeta pm_height ON pv.ID = pm_height.post_id AND pm_height.meta_key = '_height'
            LEFT JOIN {{prefix}}postmeta pm_thumbnail ON pv.ID = pm_thumbnail.post_id AND pm_thumbnail.meta_key = '_thumbnail_id'
            LEFT JOIN {{prefix}}postmeta pm_gallery ON pv.ID = pm_gallery.post_id AND pm_gallery.meta_key = '_product_image_gallery'
            LEFT JOIN {{prefix}}postmeta pm_size ON pv.ID = pm_size.post_id AND pm_size.meta_key = 'attribute_pa_size'
            WHERE pv.post_type = 'product_variation'
                AND pv.post_status = 'publish'
                AND (pm_stock_status.meta_value = 'instock' OR pm_stock_status.meta_value IS NULL)
                AND (pm_stock.meta_value > 0 OR pm_stock.meta_value IS NULL)
            ORDER BY p.ID, pv.ID
    [1] => Array
        (
            [0] => 19
            [1] => 47
            [2] => 158
            [3] => 202
            [4] => 215
            [5] => 20
            [6] => 48
            [7] => 159
            [8] => 160
            [9] => 161
            [10] => 162
            [11] => 163
            [12] => 164
            [13] => 165
            [14] => 168
            [15] => 169
            [16] => 170
            [17] => 171
            [18] => 172
            [19] => 203
            [20] => 204
            [21] => 205
            [22] => 206
            [23] => 207
            [24] => 209
            [25] => 210
            [26] => 211
            [27] => 212
            [28] => 213
            [29] => 214
            [30] => 216
            [31] => 217
            [32] => 218
            [33] => 219
            [34] => 220
            [35] => 221
            [36] => 222
            [37] => 223
            [38] => 224
            [39] => 225
            [40] => 226
        )

)

2025-06-13 07:37:04: Извлечени 2624 резултата от основната заявка
2025-06-13 07:37:04: Предварително зареждане на 48 размери и категории за 556 продукта
2025-06-13 07:37:04: Кеширани 48 атрибута за таксономия pa_size
2025-06-13 07:37:04: Кеширани категории за 556 продукта
2025-06-13 07:37:04: Извлечени 2624 вариации от категориите
