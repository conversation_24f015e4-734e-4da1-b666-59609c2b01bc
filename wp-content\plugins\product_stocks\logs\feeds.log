2025-06-13 07:16:12: Започва генериране на XML фийд за търговец: ozone
2025-06-13 07:16:12: Намерени 5 категории за търговеца: ozone
2025-06-13 07:16:12: Кешът за атрибути и категории е изчистен
2025-06-13 07:16:12: Започва извличане на вариации от категории: 19, 47, 158, 202, 215
2025-06-13 07:16:12: Извличане на вариации от категории с директна SQL заявка: Array
(
    [0] => SELECT
                pv.ID as variation_id,
                pv.post_title as variation_name,
                pv.post_parent as parent_id,
                p.post_title as product_name,
                p.post_content as product_description,
                pm_sku.meta_value as sku,
                pm_stock.meta_value as stock,
                pm_stock_status.meta_value as stock_status,
                pm_price.meta_value as price,
                pm_regular_price.meta_value as regular_price,
                pm_sale_price.meta_value as sale_price,
                pm_barcode.meta_value as barcode,
                pm_weight.meta_value as weight,
                pm_length.meta_value as length,
                pm_width.meta_value as width,
                pm_height.meta_value as height,
                pm_thumbnail.meta_value as thumbnail_id,
                pm_gallery.meta_value as gallery_images,
                pm_size.meta_value as size_slug
            FROM wpln_posts pv
            INNER JOIN wpln_posts p ON pv.post_parent = p.ID
            INNER JOIN (
                SELECT DISTINCT p_cat.ID
                FROM wpln_posts p_cat
                INNER JOIN wpln_term_relationships tr_cat ON p_cat.ID = tr_cat.object_id
                INNER JOIN wpln_term_taxonomy tt_cat ON tr_cat.term_taxonomy_id = tt_cat.term_taxonomy_id
                WHERE p_cat.post_type = 'product'
                    AND p_cat.post_status = 'publish'
                    AND tt_cat.taxonomy = 'product_cat'
                    AND tt_cat.term_id IN (19,47,158,202,215)
            ) parent_products ON p.ID = parent_products.ID
            LEFT JOIN wpln_postmeta pm_sku ON pv.ID = pm_sku.post_id AND pm_sku.meta_key = '_sku'
            LEFT JOIN wpln_postmeta pm_stock ON pv.ID = pm_stock.post_id AND pm_stock.meta_key = '_stock'
            LEFT JOIN wpln_postmeta pm_stock_status ON pv.ID = pm_stock_status.post_id AND pm_stock_status.meta_key = '_stock_status'
            LEFT JOIN wpln_postmeta pm_price ON pv.ID = pm_price.post_id AND pm_price.meta_key = '_price'
            LEFT JOIN wpln_postmeta pm_regular_price ON pv.ID = pm_regular_price.post_id AND pm_regular_price.meta_key = '_regular_price'
            LEFT JOIN wpln_postmeta pm_sale_price ON pv.ID = pm_sale_price.post_id AND pm_sale_price.meta_key = '_sale_price'
            LEFT JOIN wpln_postmeta pm_barcode ON pv.ID = pm_barcode.post_id AND pm_barcode.meta_key = '_alg_ean'
            LEFT JOIN wpln_postmeta pm_weight ON pv.ID = pm_weight.post_id AND pm_weight.meta_key = '_weight'
            LEFT JOIN wpln_postmeta pm_length ON pv.ID = pm_length.post_id AND pm_length.meta_key = '_length'
            LEFT JOIN wpln_postmeta pm_width ON pv.ID = pm_width.post_id AND pm_width.meta_key = '_width'
            LEFT JOIN wpln_postmeta pm_height ON pv.ID = pm_height.post_id AND pm_height.meta_key = '_height'
            LEFT JOIN wpln_postmeta pm_thumbnail ON pv.ID = pm_thumbnail.post_id AND pm_thumbnail.meta_key = '_thumbnail_id'
            LEFT JOIN wpln_postmeta pm_gallery ON pv.ID = pm_gallery.post_id AND pm_gallery.meta_key = '_product_image_gallery'
            LEFT JOIN wpln_postmeta pm_size ON pv.ID = pm_size.post_id AND pm_size.meta_key = 'attribute_pa_size'
            WHERE pv.post_type = 'product_variation'
                AND pv.post_status = 'publish'
                AND (pm_stock_status.meta_value = 'instock' OR pm_stock_status.meta_value IS NULL)
                AND (pm_stock.meta_value > 0 OR pm_stock.meta_value IS NULL)
            ORDER BY p.ID, pv.ID
    [1] => Array
        (
            [0] => 19
            [1] => 47
            [2] => 158
            [3] => 202
            [4] => 215
        )

)

2025-06-13 07:16:12: Извлечени 6 резултата от основната заявка
2025-06-13 07:16:12: Предварително зареждане на 3 размери и категории за 2 продукта
2025-06-13 07:16:12: Кеширани 3 атрибута за таксономия pa_size
2025-06-13 07:16:12: Кеширани категории за 2 продукта
2025-06-13 07:16:12: Извлечени 6 вариации от категориите
