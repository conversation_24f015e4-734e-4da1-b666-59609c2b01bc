<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class AV_WCProduct_Model_Feeds {
    private $db_helper;
    private $default_vendor = 'ozone';
    private $cache_dir;
    private $cache_duration = 28800; // 8 часа (3 пъти дневно)

    private $data_dir;

    // Кеширащи свойства за оптимизация на производителността
    private $cached_attributes = [];
    private $cached_categories = [];
    private $log_helper;
    private $log_file = 'feeds.log';
    
    public function __construct() {
        $this->db_helper = AV_PS_DB_Helper::getInstance();
        $data = AV_PS()->Data->getData();
        $this->data_dir = $data['data_dir'];
        
        $this->cache_dir = $this->data_dir . 'product_feeds';
        
        if (!file_exists($this->cache_dir)) {
            wp_mkdir_p($this->cache_dir);
        }
        
        $this->initializeTables();
        $this->log_helper = AV_PS()->log_helper();
    }
    
    /**
     * Инициализира необходимите таблици за фийдовете
     */
    public function initializeTables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}ps_vendor_feeds (
            id int(11) NOT NULL AUTO_INCREMENT,
            vendor_key varchar(50) NOT NULL,
            last_generated datetime DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY vendor_key (vendor_key)
        ) $charset_collate;
        
        CREATE TABLE IF NOT EXISTS {$wpdb->prefix}ps_vendor_feed_categories (
            id int(11) NOT NULL AUTO_INCREMENT,
            vendor_key varchar(50) NOT NULL,
            category_id int(11) NOT NULL,
            added_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY vendor_category (vendor_key, category_id),
            KEY vendor_key (vendor_key),
            KEY category_id (category_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Търси категории по зададен текст
     * 
     * @param string $term Текст за търсене
     * @param int $limit Брой резултати
     * @return array Масив с намерените категории
     */
    public function searchCategories($term, $limit = 20) {
        $term = trim($term);
        
        $args = [
            'taxonomy'   => 'product_cat',
            'name__like' => $term,
            'hide_empty' => false,
            'number'     => $limit,
        ];
        
        $categories = get_terms($args);
        
        $result = [];
        if (!is_wp_error($categories) && !empty($categories)) {
            foreach ($categories as $category) {
                $result[] = [
                    'id'   => $category->term_id,
                    'name' => $category->name,
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * Запазва избраните категории за търговец
     * 
     * @param string $vendor Ключ на търговеца
     * @param array $category_ids Масив с ID на категории
     * @return bool Успех или неуспех
     */
    public function saveVendorCategories($vendor, $category_ids) {
        if (empty($vendor) || empty($category_ids) || !is_array($category_ids)) {
            return false;
        }
        
        // Първо изтриваме всички съществуващи записи за този търговец
        $this->deleteVendorCategories($vendor);
        
        // Добавяме новите категории
        $values = [];
        $placeholders = [];
        $columns = ['vendor_key', 'category_id'];
        
        foreach ($category_ids as $category_id) {
            $values[] = $vendor;
            $values[] = $category_id;
            $placeholders[] = "(%s, %d)";
        }
        
        $this->db_helper->executeBatchInsert('ps_vendor_feed_categories', $columns, $placeholders, $values);
        
        // Обновяваме или създаваме запис във vendor_feeds таблицата
        $this->updateVendorFeedRecord($vendor);
        
        return true;
    }
    
    /**
     * Изтрива всички категории за търговец
     * 
     * @param string $vendor Ключ на търговеца
     * @return bool Успех или неуспех
     */
    private function deleteVendorCategories($vendor) {
        $sql = [
            "DELETE FROM {{prefix}}ps_vendor_feed_categories WHERE vendor_key = %s",
            [$vendor]
        ];
        
        return $this->db_helper->query($sql) !== false;
    }
    
    /**
     * Обновява или създава запис за търговец
     * 
     * @param string $vendor Ключ на търговеца
     * @return bool Успех или неуспех
     */
    private function updateVendorFeedRecord($vendor) {
        $check_sql = [
            "SELECT id FROM {{prefix}}ps_vendor_feeds WHERE vendor_key = %s",
            [$vendor]
        ];
        
        $vendor_id = $this->db_helper->get_var($check_sql);
        
        if ($vendor_id) {
            // Само обновяваме записа
            $sql = [
                "UPDATE {{prefix}}ps_vendor_feeds SET last_generated = NULL WHERE vendor_key = %s",
                [$vendor]
            ];
        } else {
            // Създаваме нов запис
            $sql = [
                "INSERT INTO {{prefix}}ps_vendor_feeds (vendor_key) VALUES (%s)",
                [$vendor]
            ];
        }
        
        return $this->db_helper->query($sql) !== false;
    }
    
    /**
     * Връща списък с избраните категории за търговец
     * 
     * @param string $vendor Ключ на търговеца
     * @return array Масив с ID на категории
     */
    public function getVendorCategories($vendor) {
        $sql = [
            "SELECT category_id FROM {{prefix}}ps_vendor_feed_categories WHERE vendor_key = %s",
            [$vendor]
        ];
        
        $results = $this->db_helper->get_col($sql);

        $this->log_helper->instantLog("Намерени " . count($results) . " категории за търговеца: " . $vendor, $this->log_file);

        return $results ? array_map('intval', $results) : [];
    }
    
    /**
     * Генерира публичен URL за достъп до XML фийда
     * 
     * @param string $vendor Ключ на търговеца
     * @return string URL към XML фийда
     */
    public function getPublicFeedUrl($vendor) {
        $feed_token = $this->generateFeedToken($vendor);
        $site_url = site_url();
        return $site_url . '/wp-json/product-feeds/v1/' . $vendor . '?token=' . $feed_token;
    }
    
    /**
     * Генерира токен за достъп до фийда
     * 
     * @param string $vendor Ключ на търговеца
     * @return string Токен
     */
    public function generateFeedToken($vendor) {
        $salt = get_option('product_stocks_feed_salt', '');
        
        if (empty($salt)) {
            $salt = wp_generate_password(32, false, false);
            update_option('product_stocks_feed_salt', $salt);
        }
        
        return md5($salt . $vendor . $salt);
    }
    
    /**
     * Проверява статуса на кеша за фийда
     * 
     * @param string $vendor Ключ на търговеца
     * @return array Информация за статуса на кеша
     */
    public function getCacheStatus($vendor) {
        $cache_file = $this->getCachePath($vendor);
        $exists = file_exists($cache_file);
        
        $last_generated = null;
        $size = 0;
        $is_fresh = false;
        
        if ($exists) {
            $last_generated = filemtime($cache_file);
            $size = filesize($cache_file);
            $is_fresh = (time() - $last_generated) < $this->cache_duration;
        }
        
        // Също проверка в базата данни
        $sql = [
            "SELECT last_generated FROM {{prefix}}ps_vendor_feeds WHERE vendor_key = %s",
            [$vendor]
        ];
        
        $db_last_generated = $this->db_helper->get_var($sql);
        
        return [
            'exists' => $exists,
            'last_generated' => $last_generated ? date('Y-m-d H:i:s', $last_generated) : null,
            'db_last_generated' => $db_last_generated,
            'size' => $size,
            'size_formatted' => size_format($size),
            'is_fresh' => $is_fresh,
            'needs_refresh' => !$is_fresh || !$exists,
            'path' => $cache_file
        ];
    }
    
    /**
     * Връща пътя до кеш файла
     * 
     * @param string $vendor Ключ на търговеца
     * @return string Път до кеш файла
     */
    private function getCachePath($vendor) {
        return trailingslashit($this->cache_dir) . sanitize_file_name($vendor) . '_feed.xml';
    }
    
    /**
     * Генерира и кешира XML фийд
     * 
     * @param string $vendor Ключ на търговеца
     * @return array Статус на операцията
     */
    public function generateAndCacheFeed($vendor) {
        $start_time = microtime(true);

        $this->log_helper->instantLog("Започва генериране на XML фийд за търговец: {$vendor}", $this->log_file, false);
        
        // Получаваме избраните категории
        $category_ids = $this->getVendorCategories($vendor);
        
        // Ако няма избрани категории, връщаме грешка
        if (empty($category_ids)) {
            throw new Exception('Няма избрани категории за търговеца');
        }
        
        // Генерираме XML фийда
        $xml_content = $this->generateXmlFeed($vendor, $category_ids);
        
        // Кешираме XML файла
        $cache_file = $this->getCachePath($vendor);
        $save_result = file_put_contents($cache_file, $xml_content);
        
        if ($save_result === false) {
            throw new Exception('Грешка при запазване на XML файла');
        }
        
        // Обновяваме информацията в базата данни
        $now = current_time('mysql');
        $sql = [
            "UPDATE {{prefix}}ps_vendor_feeds SET last_generated = %s WHERE vendor_key = %s",
            [$now, $vendor]
        ];
        $this->db_helper->query($sql);
        
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 2);
        
        return [
            'success' => true,
            'message' => 'XML фийдът е генериран успешно',
            'file_size' => size_format(filesize($cache_file)),
            'execution_time' => $execution_time,
            'products_count' => $this->last_products_count ?? 0,
            'generated_time' => $now,
        ];
    }
    
    /**
     * Променлива за съхраняване на броя на продуктите във фийда
     */
    private $last_products_count = 0;
    
    /**
     * Генерира XML фийда
     * 
     * @param string $vendor Ключ на търговеца
     * @param array $category_ids ID-та на категориите
     * @return string XML съдържание
     */
    public function generateXmlFeed($vendor, $category_ids) {
        // Вземаме продуктите от избраните категории
        $products = $this->getProductsFromCategories($category_ids);
        
        $this->last_products_count = count($products);
        
        // Създаваме XML фийда
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><itemlist></itemlist>');
        
        foreach ($products as $product) {
            $item = $xml->addChild('item');
            
            // Добавяме данните за продукта
            $this->addProductDataToXml($item, $product);
        }
        
        return $xml->asXML();
    }
    
    /**
     * Взима вариациите от избраните категории
     *
     * @param array $category_ids ID-та на категориите
     * @return array Масив с вариации
     */
    private function getProductsFromCategories($category_ids) {
        // Изчистване на кеша в началото на всяко генериране
        $this->clearCache();

        // Логване на началото на операцията
        $this->log_helper->instantLog("Започва извличане на вариации от категории: " . implode(', ', $category_ids), $this->log_file);

        // Извличане на вариации от избраните категории с директна SQL заявка
        $variations = $this->getVariationsFromCategories($category_ids);

        $this->log_helper->instantLog("Извлечени " . count($variations) . " вариации от категориите", $this->log_file);

        return $variations;
    }

    /**
     * Изчиства кеша за атрибути и категории
     */
    private function clearCache() {
        $this->cached_attributes = [];
        $this->cached_categories = [];
        
        $this->log_helper->instantLog("Кешът за атрибути и категории е изчистен", $this->log_file);
    }

    /**
     * Извлича вариации от категории с директна SQL заявка
     *
     * @param array $category_ids ID-та на категориите
     * @return array Масив с вариации
     */
    private function getVariationsFromCategories($category_ids) {
        if (empty($category_ids)) {
            return [];
        }

        // Извличане на всички подкатегории (включително избраните категории)
        $all_category_ids = $this->getAllSubcategories($category_ids);

        if (AV_PS()->isDeveloper()) {
            AV_PS()->log_helper()->instantLog("Оригинални категории: " . implode(', ', $category_ids) . "; Всички категории (с подкатегории): " . implode(', ', $all_category_ids));
        }

        $placeholders = implode(',', array_fill(0, count($all_category_ids), '%d'));

        // SQL заявка за извличане на вариации от категории
        // Първо намираме родителските продукти в категориите, след това техните вариации
        $query = [
            "SELECT
                pv.ID as variation_id,
                pv.post_title as variation_name,
                pv.post_parent as parent_id,
                p.post_title as product_name,
                p.post_content as product_description,
                pm_sku.meta_value as sku,
                pm_stock.meta_value as stock,
                pm_stock_status.meta_value as stock_status,
                pm_price.meta_value as price,
                pm_regular_price.meta_value as regular_price,
                pm_sale_price.meta_value as sale_price,
                pm_barcode.meta_value as barcode,
                pm_weight.meta_value as weight,
                pm_length.meta_value as length,
                pm_width.meta_value as width,
                pm_height.meta_value as height,
                pm_thumbnail.meta_value as thumbnail_id,
                pm_gallery.meta_value as gallery_images,
                pm_size.meta_value as size_slug
            FROM {{prefix}}posts pv
            INNER JOIN {{prefix}}posts p ON pv.post_parent = p.ID
            INNER JOIN (
                SELECT DISTINCT p_cat.ID
                FROM {{prefix}}posts p_cat
                INNER JOIN {{prefix}}term_relationships tr_cat ON p_cat.ID = tr_cat.object_id
                INNER JOIN {{prefix}}term_taxonomy tt_cat ON tr_cat.term_taxonomy_id = tt_cat.term_taxonomy_id
                WHERE p_cat.post_type = 'product'
                    AND p_cat.post_status = 'publish'
                    AND tt_cat.taxonomy = 'product_cat'
                    AND tt_cat.term_id IN ($placeholders)
            ) parent_products ON p.ID = parent_products.ID
            LEFT JOIN {{prefix}}postmeta pm_sku ON pv.ID = pm_sku.post_id AND pm_sku.meta_key = '_sku'
            LEFT JOIN {{prefix}}postmeta pm_stock ON pv.ID = pm_stock.post_id AND pm_stock.meta_key = '_stock'
            LEFT JOIN {{prefix}}postmeta pm_stock_status ON pv.ID = pm_stock_status.post_id AND pm_stock_status.meta_key = '_stock_status'
            LEFT JOIN {{prefix}}postmeta pm_price ON pv.ID = pm_price.post_id AND pm_price.meta_key = '_price'
            LEFT JOIN {{prefix}}postmeta pm_regular_price ON pv.ID = pm_regular_price.post_id AND pm_regular_price.meta_key = '_regular_price'
            LEFT JOIN {{prefix}}postmeta pm_sale_price ON pv.ID = pm_sale_price.post_id AND pm_sale_price.meta_key = '_sale_price'
            LEFT JOIN {{prefix}}postmeta pm_barcode ON pv.ID = pm_barcode.post_id AND pm_barcode.meta_key = '_alg_ean'
            LEFT JOIN {{prefix}}postmeta pm_weight ON pv.ID = pm_weight.post_id AND pm_weight.meta_key = '_weight'
            LEFT JOIN {{prefix}}postmeta pm_length ON pv.ID = pm_length.post_id AND pm_length.meta_key = '_length'
            LEFT JOIN {{prefix}}postmeta pm_width ON pv.ID = pm_width.post_id AND pm_width.meta_key = '_width'
            LEFT JOIN {{prefix}}postmeta pm_height ON pv.ID = pm_height.post_id AND pm_height.meta_key = '_height'
            LEFT JOIN {{prefix}}postmeta pm_thumbnail ON pv.ID = pm_thumbnail.post_id AND pm_thumbnail.meta_key = '_thumbnail_id'
            LEFT JOIN {{prefix}}postmeta pm_gallery ON pv.ID = pm_gallery.post_id AND pm_gallery.meta_key = '_product_image_gallery'
            LEFT JOIN {{prefix}}postmeta pm_size ON pv.ID = pm_size.post_id AND pm_size.meta_key = 'attribute_pa_size'
            WHERE pv.post_type = 'product_variation'
                AND pv.post_status = 'publish'
                AND (pm_stock_status.meta_value = 'instock' OR pm_stock_status.meta_value IS NULL)
                AND (pm_stock.meta_value > 0 OR pm_stock.meta_value IS NULL)
            ORDER BY p.ID, pv.ID",
            $all_category_ids
        ];

        $this->log_helper->instantLog("Извличане на вариации от категории с директна SQL заявка: " . print_r($query, true), $this->log_file);

        $results = $this->db_helper->get_results_array($query);

        $this->log_helper->instantLog("Извлечени " . count($results) . " резултата от основната заявка", $this->log_file);
        
        // Предварително зареждане на атрибути и категории за оптимизация
        $this->preloadAttributesAndCategories($results);

        // Обработка на резултатите с използване на кешираните данни
        $variations = [];
        foreach ($results as $result) {
            $variation = $this->processVariationData($result);
            $variations[] = $variation;
        }

        return $variations;
    }

    /**
     * Извлича всички подкатегории (включително избраните категории) рекурсивно
     *
     * @param array $category_ids ID-та на категориите
     * @return array Масив с всички ID-та на категории и подкатегории
     */
    private function getAllSubcategories($category_ids) {
        if (empty($category_ids)) {
            return [];
        }

        $all_categories = $category_ids; // Започваме с оригиналните категории
        $categories_to_process = $category_ids;

        // Рекурсивно извличане на подкатегории до 5 нива дълбочина (за безопасност)
        $max_depth = 5;
        $current_depth = 0;

        while (!empty($categories_to_process) && $current_depth < $max_depth) {
            $placeholders = implode(',', array_fill(0, count($categories_to_process), '%d'));

            $query = [
                "SELECT DISTINCT tt.term_id
                 FROM {{prefix}}term_taxonomy tt
                 WHERE tt.taxonomy = 'product_cat'
                   AND tt.parent IN ($placeholders)",
                $categories_to_process
            ];

            $subcategories = $this->db_helper->get_results_array($query);
            $new_category_ids = array_column($subcategories, 'term_id');

            if (empty($new_category_ids)) {
                break; // Няма повече подкатегории
            }

            // Добавяме новите подкатегории към общия списък
            $all_categories = array_merge($all_categories, $new_category_ids);

            // Следващата итерация ще търси подкатегории на новонамерените категории
            $categories_to_process = $new_category_ids;
            $current_depth++;
        }

        // Премахване на дублирани ID-та
        $all_categories = array_unique($all_categories);

        
        $this->log_helper->instantLog("Извлечени " . count($all_categories) . " категории (включително подкатегории) на дълбочина $current_depth", $this->log_file);
       

        return $all_categories;
    }

    /**
     * Предварително зарежда атрибути и категории за оптимизация на производителността
     *
     * @param array $results Резултати от основната SQL заявка
     */
    private function preloadAttributesAndCategories($results) {
        if (empty($results)) {
            return;
        }

        // Събиране на уникални slug-ове за атрибути и product ID-та
        $size_slugs = [];
        $product_ids = [];

        foreach ($results as $result) {
            if (!empty($result['size_slug'])) {
                $size_slugs[] = $result['size_slug'];
            }
            if (!empty($result['parent_id'])) {
                $product_ids[] = $result['parent_id'];
            }
        }

        // Премахване на дублирани стойности
        $size_slugs = array_unique($size_slugs);
        $product_ids = array_unique($product_ids);

        $this->log_helper->instantLog("Предварително зареждане на " . count($size_slugs) . " размери и категории за " . count($product_ids) . " продукта", $this->log_file);

        // Предварително зареждане на атрибути
        $this->preloadAttributes('pa_size', $size_slugs);

        // Предварително зареждане на категории
        $this->preloadCategories($product_ids);
    }

    /**
     * Предварително зарежда атрибути по таксономия и slug-ове
     *
     * @param string $taxonomy Таксономия на атрибута
     * @param array $slugs Масив със slug-ове
     */
    private function preloadAttributes($taxonomy, $slugs) {
        if (empty($slugs)) {
            return;
        }

        $placeholders = implode(',', array_fill(0, count($slugs), '%s'));
        $query = [
            "SELECT t.slug, t.name FROM {{prefix}}terms t
             INNER JOIN {{prefix}}term_taxonomy tt ON t.term_id = tt.term_id
             WHERE tt.taxonomy = %s AND t.slug IN ($placeholders)",
            array_merge([$taxonomy], $slugs)
        ];

        $results = $this->db_helper->get_results_array($query);

        // Кеширане на резултатите
        if (!isset($this->cached_attributes[$taxonomy])) {
            $this->cached_attributes[$taxonomy] = [];
        }

        foreach ($results as $result) {
            $this->cached_attributes[$taxonomy][$result['slug']] = $result['name'];
        }

       $this->log_helper->instantLog("Кеширани " . count($results) . " атрибута за таксономия $taxonomy", $this->log_file);
    }

    /**
     * Предварително зарежда категории за продукти
     *
     * @param array $product_ids Масив с ID-та на продукти
     */
    private function preloadCategories($product_ids) {
        if (empty($product_ids)) {
            return;
        }

        $placeholders = implode(',', array_fill(0, count($product_ids), '%d'));
        $query = [
            "SELECT tr.object_id as product_id, t.term_id, t.name, t.slug
             FROM {{prefix}}terms t
             INNER JOIN {{prefix}}term_taxonomy tt ON t.term_id = tt.term_id
             INNER JOIN {{prefix}}term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
             WHERE tr.object_id IN ($placeholders) AND tt.taxonomy = 'product_cat'
             ORDER BY tr.object_id, t.name",
            $product_ids
        ];

        $results = $this->db_helper->get_results_array($query);

        // Групиране на категориите по product_id
        foreach ($results as $result) {
            $product_id = $result['product_id'];
            if (!isset($this->cached_categories[$product_id])) {
                $this->cached_categories[$product_id] = [];
            }

            $this->cached_categories[$product_id][] = [
                'term_id' => $result['term_id'],
                'name' => $result['name'],
                'slug' => $result['slug']
            ];
        }

        $this->log_helper->instantLog("Кеширани категории за " . count($this->cached_categories) . " продукта", $this->log_file);
    }

    /**
     * Обработва данните за вариация
     *
     * @param array $result Резултат от SQL заявката
     * @return array Обработени данни за вариацията
     */
    private function processVariationData($result) {
        $variation = [
            'variation_id' => $result['variation_id'],
            'variation_name' => $result['variation_name'],
            'parent_id' => $result['parent_id'],
            'product_name' => $result['product_name'],
            'product_description' => $result['product_description'],
            'sku' => $result['sku'],
            'stock' => $result['stock'],
            'stock_status' => $result['stock_status'],
            'price' => $result['price'],
            'regular_price' => $result['regular_price'],
            'sale_price' => $result['sale_price'],
            'barcode' => $result['barcode'],
            'weight' => $result['weight'],
            'length' => $result['length'],
            'width' => $result['width'],
            'height' => $result['height'],
            'thumbnail_id' => $result['thumbnail_id'],
            'gallery_images' => $result['gallery_images'],
            'size_slug' => $result['size_slug'],
        ];

        // Извличане на имената на атрибутите от кешираните данни
        $variation['size_name'] = $this->getCachedAttributeName('pa_size', $result['size_slug']);

        // Извличане на категории от кешираните данни
        $variation['categories'] = $this->getCachedProductCategories($result['parent_id']);

        return $variation;
    }

    /**
     * Извлича име на атрибут от кешираните данни
     *
     * @param string $taxonomy Таксономия на атрибута
     * @param string $slug Slug на атрибута
     * @return string Име на атрибута
     */
    private function getCachedAttributeName($taxonomy, $slug) {
        if (empty($slug)) {
            return '';
        }

        // Проверка дали атрибутът е кеширан
        if (isset($this->cached_attributes[$taxonomy][$slug])) {
            return $this->cached_attributes[$taxonomy][$slug];
        }


        $this->log_helper->instantLog("Предупреждение: Атрибут $taxonomy:$slug не е кеширан, използва се fallback заявка");
    

        return $this->getAttributeNameFromSlug($taxonomy, $slug);
    }

    /**
     * Извлича категории за продукт от кешираните данни
     *
     * @param int $product_id ID на продукта
     * @return array Масив с категории
     */
    private function getCachedProductCategories($product_id) {
        // Проверка дали категориите са кеширани
        if (isset($this->cached_categories[$product_id])) {
            return $this->cached_categories[$product_id];
        }

        // Fallback към директна заявка ако не са кеширани (не трябва да се случва)
        
        $this->log_helper->instantLog("Предупреждение: Категории за продукт $product_id не са кеширани, използва се fallback заявка");
        

        return $this->getProductCategories($product_id);
    }

    /**
     * Извлича име на атрибут от slug (fallback метод)
     *
     * @param string $taxonomy Таксономия на атрибута
     * @param string $slug Slug на атрибута
     * @return string Име на атрибута
     */
    private function getAttributeNameFromSlug($taxonomy, $slug) {
        if (empty($slug)) {
            return '';
        }

        $query = [
            "SELECT t.name FROM {{prefix}}terms t
             INNER JOIN {{prefix}}term_taxonomy tt ON t.term_id = tt.term_id
             WHERE tt.taxonomy = %s AND t.slug = %s",
            [$taxonomy, $slug]
        ];

        return $this->db_helper->get_var($query) ?: '';
    }

    /**
     * Извлича категориите за продукт
     *
     * @param int $product_id ID на продукта
     * @return array Масив с категории
     */
    private function getProductCategories($product_id) {
        $query = [
            "SELECT t.term_id, t.name, t.slug FROM {{prefix}}terms t
             INNER JOIN {{prefix}}term_taxonomy tt ON t.term_id = tt.term_id
             INNER JOIN {{prefix}}term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
             WHERE tr.object_id = %d AND tt.taxonomy = 'product_cat'
             ORDER BY t.name",
            [$product_id]
        ];

        return $this->db_helper->get_results_array($query);
    }

    /**
     * Добавя данните за вариацията към XML елемента
     *
     * @param SimpleXMLElement $item XML елемент
     * @param array $variation Данни за вариацията
     */
    private function addProductDataToXml($item, $variation) {
        // Основни данни за вариацията
        $this->addCdataChild($item, 'Product_ID', $variation['variation_id']);
        $this->addCdataChild($item, 'Product_name', $variation['product_name']);
        $this->addCdataChild($item, 'Variation_name', $variation['variation_name']);

        // Описание
        $description = $variation['product_description'];
        $this->addCdataChild($item, 'Description', $description);

        // SKU/Model - използваме SKU на вариацията
        $this->addCdataChild($item, 'Model', $variation['sku']);
        $this->addCdataChild($item, 'SKU', $variation['sku']);

        // Цена
        $price = $variation['regular_price'];
        $sale_price = $variation['sale_price'];
        $current_price = $variation['price'];

        $this->addCdataChild($item, 'Price', $price);
        $this->addCdataChild($item, 'Special_price', $sale_price);
        $this->addCdataChild($item, 'Current_price', $current_price);

        // Размери и тегло
        $weight = $variation['weight'] ? $variation['weight'] . 'кг.' : '';
        $this->addCdataChild($item, 'Weight', $weight);
        $this->addCdataChild($item, 'Length', $variation['length']);
        $this->addCdataChild($item, 'Width', $variation['width']);
        $this->addCdataChild($item, 'Height', $variation['height']);

        // Атрибути на вариацията
        $this->addCdataChild($item, 'Size', $variation['size_name']);

        // Категории
        $categories = $variation['categories'];
        if (!empty($categories)) {
            $main_category = $categories[0];
            $this->addCdataChild($item, 'Category', $main_category['name']);

            $category_names = array_column($categories, 'name');
            $category_tree = $this->getCategoryTree($category_names);
            $this->addCdataChild($item, 'Categories', $category_tree);
        }

        // Количество
        $stock_quantity = $variation['stock'] ? intval($variation['stock']) : 0;
        $this->addCdataChild($item, 'Qantity', $stock_quantity);

        // Изображения
        if ($variation['thumbnail_id']) {
            $image_url = wp_get_attachment_image_url($variation['thumbnail_id'], 'full');
            if ($image_url) {
                $this->addCdataChild($item, 'Main_image', $image_url);
            }
        }

        // Допълнителни изображения
        if ($variation['gallery_images']) {
            $gallery_ids = explode(',', $variation['gallery_images']);
            $gallery_urls = [];

            foreach ($gallery_ids as $gallery_id) {
                $gallery_id = trim($gallery_id);
                if ($gallery_id) {
                    $gallery_url = wp_get_attachment_image_url($gallery_id, 'full');
                    if ($gallery_url) {
                        $gallery_urls[] = $gallery_url;
                    }
                }
            }

            if (!empty($gallery_urls)) {
                $this->addCdataChild($item, 'Additional_images', implode('|', $gallery_urls));
            }
        }

        // Баркод/EAN код
        if ($variation['barcode']) {
            $this->addCdataChild($item, 'EAN', $variation['barcode']);
            $this->addCdataChild($item, 'Barcode', $variation['barcode']);
        }

        // Статус
        $is_in_stock = ($variation['stock_status'] === 'instock' && $stock_quantity > 0);
        $this->addCdataChild($item, 'Status', $is_in_stock ? '1' : '0');
    }
    
    /**
     * Създава дърво от категории във формат category1|category1>subcategory
     * 
     * @param array $categories Масив с категории
     * @return string Форматирано дърво от категории
     */
    private function getCategoryTree($categories) {
        if (count($categories) <= 1) {
            return isset($categories[0]) ? $categories[0] : '';
        }
        
        $main = $categories[0];
        $result = $main;
        
        for ($i = 1; $i < count($categories); $i++) {
            $result .= '|' . $main . '>' . $categories[$i];
        }
        
        return $result;
    }
    
    /**
     * Помощен метод за добавяне на CDATA съдържание към XML елемент
     * 
     * @param SimpleXMLElement $parent Родителски елемент
     * @param string $name Име на елемента
     * @param string $value Стойност на елемента
     */
    private function addCdataChild($parent, $name, $value) {
        $node = $parent->addChild($name);
        $dom = dom_import_simplexml($node);
        $cdata = $dom->ownerDocument->createCDATASection($value);
        $dom->appendChild($cdata);
    }
}